<template>
  <div class="fraction-calculator">
    <div class="calculator-header">
      <h4 class="text-sm font-medium text-gray-700 mb-2">分数计算器</h4>
    </div>
    
    <div class="calculator-content">
      <!-- 分数输入 -->
      <div class="fraction-inputs">
        <div class="fraction-input-group">
          <label class="input-label">第一个分数</label>
          <div class="fraction-input">
            <input 
              v-model.number="fraction1.whole" 
              type="number" 
              min="0" 
              placeholder="整数"
              class="whole-input"
            >
            <div class="fraction-display">
              <input 
                v-model.number="fraction1.numerator" 
                type="number" 
                min="1" 
                placeholder="分子"
                class="numerator-input"
              >
              <div class="fraction-line"></div>
              <input 
                v-model.number="fraction1.denominator" 
                type="number" 
                min="1" 
                placeholder="分母"
                class="denominator-input"
              >
            </div>
          </div>
        </div>

        <div class="operation-selector">
          <select v-model="operation" class="operation-select">
            <option value="+">+（加法）</option>
            <option value="-">-（减法）</option>
          </select>
        </div>

        <div class="fraction-input-group">
          <label class="input-label">第二个分数</label>
          <div class="fraction-input">
            <input 
              v-model.number="fraction2.whole" 
              type="number" 
              min="0" 
              placeholder="整数"
              class="whole-input"
            >
            <div class="fraction-display">
              <input 
                v-model.number="fraction2.numerator" 
                type="number" 
                min="1" 
                placeholder="分子"
                class="numerator-input"
              >
              <div class="fraction-line"></div>
              <input 
                v-model.number="fraction2.denominator" 
                type="number" 
                min="1" 
                placeholder="分母"
                class="denominator-input"
              >
            </div>
          </div>
        </div>
      </div>

      <!-- 计算按钮 -->
      <div class="calculate-section">
        <button @click="calculate" class="calculate-btn">
          计算结果
        </button>
      </div>

      <!-- 结果显示 -->
      <div v-if="result" class="result-section">
        <div class="result-display">
          <h5 class="result-title">计算结果：</h5>
          <div class="result-formula">
            <MathFormula :formula="result.latex" />
          </div>
        </div>
        
        <div class="steps-display">
          <h5 class="steps-title">计算步骤：</h5>
          <ol class="steps-list">
            <li v-for="(step, index) in result.steps" :key="index" class="step-item">
              {{ step }}
            </li>
          </ol>
        </div>

        <div class="insert-section">
          <button @click="insertResult" class="insert-btn">
            插入到编辑器
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import MathFormula from './MathFormula.vue'

interface Fraction {
  whole: number
  numerator: number
  denominator: number
}

interface CalculationResult {
  latex: string
  steps: string[]
  simplified: {
    whole: number
    numerator: number
    denominator: number
  }
}

interface Emits {
  (e: 'insert-formula', formula: string): void
}

const emit = defineEmits<Emits>()

const fraction1 = ref<Fraction>({
  whole: 0,
  numerator: 1,
  denominator: 2
})

const fraction2 = ref<Fraction>({
  whole: 0,
  numerator: 1,
  denominator: 4
})

const operation = ref<'+' | '-'>('+')
const result = ref<CalculationResult | null>(null)

// 最大公约数
const gcd = (a: number, b: number): number => {
  return b === 0 ? a : gcd(b, a % b)
}

// 最小公倍数
const lcm = (a: number, b: number): number => {
  return (a * b) / gcd(a, b)
}

// 化简分数
const simplifyFraction = (numerator: number, denominator: number) => {
  const g = gcd(Math.abs(numerator), Math.abs(denominator))
  return {
    numerator: numerator / g,
    denominator: denominator / g
  }
}

// 将带分数转换为假分数
const toImproperFraction = (fraction: Fraction) => {
  return {
    numerator: fraction.whole * fraction.denominator + fraction.numerator,
    denominator: fraction.denominator
  }
}

// 将假分数转换为带分数
const toMixedNumber = (numerator: number, denominator: number) => {
  const whole = Math.floor(Math.abs(numerator) / denominator)
  const remainder = Math.abs(numerator) % denominator
  const sign = numerator < 0 ? -1 : 1
  
  return {
    whole: whole * sign,
    numerator: remainder,
    denominator: denominator
  }
}

// 格式化分数为LaTeX
const formatFractionLatex = (fraction: Fraction) => {
  if (fraction.whole === 0) {
    return `\\frac{${fraction.numerator}}{${fraction.denominator}}`
  } else if (fraction.numerator === 0) {
    return `${fraction.whole}`
  } else {
    return `${fraction.whole}\\frac{${fraction.numerator}}{${fraction.denominator}}`
  }
}

const calculate = () => {
  try {
    // 转换为假分数
    const f1 = toImproperFraction(fraction1.value)
    const f2 = toImproperFraction(fraction2.value)
    
    // 找到公分母
    const commonDenominator = lcm(f1.denominator, f2.denominator)
    
    // 通分
    const f1Numerator = f1.numerator * (commonDenominator / f1.denominator)
    const f2Numerator = f2.numerator * (commonDenominator / f2.denominator)
    
    // 计算结果
    let resultNumerator: number
    if (operation.value === '+') {
      resultNumerator = f1Numerator + f2Numerator
    } else {
      resultNumerator = f1Numerator - f2Numerator
    }
    
    // 化简结果
    const simplified = simplifyFraction(resultNumerator, commonDenominator)
    const mixedResult = toMixedNumber(simplified.numerator, simplified.denominator)
    
    // 生成计算步骤
    const steps: string[] = []
    
    // 原始表达式
    const f1Latex = formatFractionLatex(fraction1.value)
    const f2Latex = formatFractionLatex(fraction2.value)
    steps.push(`原式：${f1Latex} ${operation.value} ${f2Latex}`)
    
    // 转换为假分数（如果需要）
    if (fraction1.value.whole > 0 || fraction2.value.whole > 0) {
      steps.push(`转换为假分数：\\frac{${f1.numerator}}{${f1.denominator}} ${operation.value} \\frac{${f2.numerator}}{${f2.denominator}}`)
    }
    
    // 通分
    if (f1.denominator !== f2.denominator) {
      steps.push(`通分：\\frac{${f1Numerator}}{${commonDenominator}} ${operation.value} \\frac{${f2Numerator}}{${commonDenominator}}`)
    }
    
    // 计算
    steps.push(`计算：\\frac{${f1Numerator} ${operation.value} ${f2Numerator}}{${commonDenominator}} = \\frac{${resultNumerator}}{${commonDenominator}}`)
    
    // 化简
    if (simplified.numerator !== resultNumerator || simplified.denominator !== commonDenominator) {
      steps.push(`化简：\\frac{${simplified.numerator}}{${simplified.denominator}}`)
    }
    
    // 转换为带分数（如果需要）
    let finalLatex: string
    if (mixedResult.whole !== 0 && mixedResult.numerator !== 0) {
      finalLatex = `${mixedResult.whole}\\frac{${mixedResult.numerator}}{${mixedResult.denominator}}`
      steps.push(`转换为带分数：${finalLatex}`)
    } else if (mixedResult.whole !== 0 && mixedResult.numerator === 0) {
      finalLatex = `${mixedResult.whole}`
      steps.push(`结果为整数：${finalLatex}`)
    } else {
      finalLatex = `\\frac{${simplified.numerator}}{${simplified.denominator}}`
    }
    
    // 生成完整的LaTeX表达式
    const fullLatex = `${f1Latex} ${operation.value} ${f2Latex} = ${finalLatex}`
    
    result.value = {
      latex: fullLatex,
      steps,
      simplified: mixedResult
    }
    
  } catch (error) {
    console.error('计算错误:', error)
    alert('计算出错，请检查输入的数值')
  }
}

const insertResult = () => {
  if (result.value) {
    emit('insert-formula', `$${result.value.latex}$`)
  }
}
</script>

<style scoped>
.fraction-calculator {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  max-height: 600px;
  overflow-y: auto;
}

.calculator-header {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
  margin-bottom: 16px;
}

.fraction-inputs {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 16px;
}

.fraction-input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #6b7280;
}

.fraction-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.whole-input {
  width: 60px;
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  text-align: center;
}

.fraction-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.numerator-input,
.denominator-input {
  width: 50px;
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  text-align: center;
}

.fraction-line {
  width: 60px;
  height: 1px;
  background: #374151;
}

.operation-selector {
  display: flex;
  justify-content: center;
  margin: 8px 0;
}

.operation-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
}

.calculate-section {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.calculate-btn {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.calculate-btn:hover {
  background: #2563eb;
}

.result-section {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.result-display {
  margin-bottom: 16px;
}

.result-title,
.steps-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.result-formula {
  padding: 12px;
  background: #f9fafb;
  border-radius: 4px;
  text-align: center;
}

.steps-display {
  margin-bottom: 16px;
}

.steps-list {
  list-style: decimal;
  padding-left: 20px;
}

.step-item {
  margin-bottom: 4px;
  font-size: 0.875rem;
  color: #6b7280;
}

.insert-section {
  display: flex;
  justify-content: center;
}

.insert-btn {
  padding: 6px 12px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
}

.insert-btn:hover {
  background: #059669;
}
</style>
