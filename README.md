# PrintMind - 智能排版工具

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Vue 3](https://img.shields.io/badge/Vue-3.x-4FC08D.svg)](https://vuejs.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.68+-009688.svg)](https://fastapi.tiangolo.com/)
[![Docker](https://img.shields.io/badge/Docker-Ready-2496ED.svg)](https://www.docker.com/)

PrintMind 是一个基于 Web 的智能排版工具，集成了强大的 AI 助手和数学公式支持。支持 Markdown/Word 文档上传、可视化配置印刷参数、AI 优化排版决策、数学公式渲染、分数计算器，并能导出印刷级 PDF。特别适合教育工作者、学生和需要处理数学内容的用户。

## ✨ 核心亮点

🧮 **强大的数学支持** - 完整的 LaTeX 公式渲染，交互式分数计算器，算式智能并排显示
🤖 **AI 智能助手** - 集成 Doubao AI，提供排版建议、题目生成、文档校对
📚 **教育专用功能** - 考试卷生成、教师版/学生版切换、专业教育样式
🎨 **专业排版** - 支持中文字体、CMYK 颜色、600 DPI 高清输出
⚡ **实时预览** - HTML/PDF 双模式预览，所见即所得
🔧 **易于使用** - 拖拽上传、可视化配置、一键导出

## 🎯 在线体验

### 🚀 一键部署到云端

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/H2Cat96/PrintMind&build-command=cd%20frontend%20%26%26%20npm%20ci%20%26%26%20npm%20run%20build&output-directory=frontend%2Fdist&install-command=cd%20frontend%20%26%26%20npm%20ci)

[![Deploy on Railway](https://railway.app/button.svg)](https://railway.app/new/template?template=https://github.com/H2Cat96/PrintMind&envs=DEEPSEEK_API_KEY)

[![Deploy to Render](https://render.com/images/deploy-to-render-button.svg)](https://render.com/deploy?repo=https://github.com/H2Cat96/PrintMind)

### 📱 在线演示

> **即将上线**: 我们正在准备在线演示环境，敬请期待！

## 📸 项目截图

*即将添加项目界面截图...*

## 🚀 功能特性

### 📝 文档处理
- 📁 **文件上传**: 支持拖拽上传 Markdown、Word、TXT 文档
- ✏️ **实时编辑**: 内置 Markdown 编辑器，支持语法高亮和工具栏
- 👁️ **实时预览**: HTML/PDF 双模式预览，支持缩放和版本切换
- 🔄 **格式转换**: Word 转 Markdown 服务，保持格式完整性

### 🧮 数学功能
- 📐 **LaTeX 公式**: 完整支持 LaTeX 数学公式渲染，高清晰度输出
- 🔢 **分数计算器**: 交互式分数加减法计算器，支持带分数
- 📊 **智能排列**: 算式自动并排显示，优化页面空间利用
- 🎯 **公式模板**: 内置常用数学公式模板，快速插入

### 🤖 AI 智能助手
- 💬 **智能对话**: 集成 Doubao AI，提供排版建议和内容优化
- 📝 **题目生成**: 自动生成选择题、填空题、判断题、应用题、算数题
- 🔍 **文档校对**: AI 驱动的错别字检查和语法纠错
- 🖼️ **图像分析**: 支持图片上传和内容分析

### ⚙️ 排版配置
- 📄 **版式设置**: 可视化配置页面格式、边距、字体、DPI 等参数
- 🎨 **字体管理**: 系统字体检测，支持楷体、阿里巴巴普惠体等中文字体
- 🖨️ **印刷级输出**: 支持 CMYK 颜色模式和出血设置
- ✅ **参数验证**: 智能检查排版参数合理性

### 🎯 教育专用功能
- 📚 **考试卷生成**: 一键生成各类题型的考试卷
- 👨‍🏫 **教师版/学生版**: 支持答案显示/隐藏切换
- 📊 **题目统计**: 智能分配题目类型和数量
- 🎨 **专业样式**: 内置教育文档样式模板

## 🛠️ 技术栈

### 前端技术
- **框架**: Vue 3 + TypeScript + Composition API
- **样式**: Tailwind CSS + 响应式设计
- **组件**: 自定义组件库，支持拖拽和实时预览
- **数学渲染**: MathJax + LaTeX 支持

### 后端技术
- **API框架**: FastAPI + Pydantic 数据验证
- **PDF生成**: WeasyPrint + ReportLab 混合架构
- **文档处理**: Python-docx + Markdown 解析
- **数学计算**: Matplotlib + LaTeX 公式渲染
- **AI集成**: Doubao AI API + 智能对话

### 部署方案
- **容器化**: Docker + Docker Compose
- **云部署**: 支持 Vercel、Railway、Render 一键部署
- **本地开发**: 热重载 + 开发环境隔离

## 📦 快速开始

### 🚀 一键部署（推荐）

1. **克隆项目**
```bash
git clone https://github.com/H2Cat96/PrintMind.git
cd PrintMind
```

2. **配置环境变量（可选）**
```bash
cp .env.example .env
# 编辑 .env 文件，配置 AI 服务
# DOUBAO_API_KEY=your_doubao_api_key_here
```

3. **启动服务**
```bash
docker-compose up -d
```

4. **访问应用**
- 前端: http://localhost
- 后端 API: http://localhost:8000
- API 文档: http://localhost:8000/docs

### 本地开发

#### 后端开发

1. **安装依赖**
```bash
cd backend
pip install -r requirements.txt
```

2. **启动后端服务**
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端开发

1. **安装依赖**
```bash
cd frontend
npm install
```

2. **启动开发服务器**
```bash
npm run dev
```

## 📁 项目结构

```
PrintMind/
├── frontend/                 # Vue 3 前端
│   ├── src/
│   │   ├── components/      # 组件
│   │   │   ├── FileUpload.vue
│   │   │   ├── ConfigPanel.vue
│   │   │   ├── MarkdownEditor.vue
│   │   │   └── PDFPreview.vue
│   │   ├── views/          # 页面
│   │   ├── types/          # TypeScript 类型
│   │   └── utils/          # 工具函数
│   └── package.json
├── backend/                 # FastAPI 后端
│   ├── app/
│   │   ├── api/            # API 路由
│   │   │   ├── documents.py
│   │   │   ├── layout.py
│   │   │   ├── pdf.py
│   │   │   └── fonts.py
│   │   ├── services/       # 业务逻辑
│   │   │   ├── document_service.py
│   │   │   ├── layout_service.py
│   │   │   ├── pdf_service.py
│   │   │   └── font_service.py
│   │   ├── models/         # 数据模型
│   │   └── core/           # 核心配置
│   └── requirements.txt
├── docker-compose.yml       # Docker 编排
├── Dockerfile.frontend      # 前端 Docker 文件
├── Dockerfile.backend       # 后端 Docker 文件
└── README.md
```

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 | 必需 |
|--------|------|--------|------|
| `DOUBAO_API_KEY` | Doubao AI API 密钥 | - | 否* |
| `DOUBAO_API_ENDPOINT` | Doubao API 端点 | `https://ark.cn-beijing.volces.com/api/v3/chat/completions` | 否 |
| `DOUBAO_MODEL` | Doubao 模型名称 | `doubao-seed-1-6-250615` | 否 |
| `DEBUG` | 调试模式 | `true` | 否 |
| `MAX_FILE_SIZE` | 最大文件大小 | `52428800` (50MB) | 否 |
| `PDF_DPI` | PDF 分辨率 | `600` | 否 |

> *AI 功能需要配置 API 密钥，其他功能可正常使用

### 排版配置

支持的页面格式：
- A4 (21cm × 29.7cm)
- A3 (29.7cm × 42cm)
- Letter (8.5in × 11in)
- Legal (8.5in × 14in)

支持的颜色模式：
- RGB (屏幕显示)
- CMYK (印刷)

## 📖 使用指南

### 1. 📁 文档上传与编辑
- **文件上传**: 支持拖拽或点击上传 `.md`, `.docx`, `.txt` 文件
- **在线编辑**: 使用内置 Markdown 编辑器进行实时编辑
- **文件管理**: 支持多文件管理，最近上传历史记录

### 2. 🧮 数学公式功能
- **LaTeX 公式**: 使用 `$公式$` 或 `$$公式$$` 插入数学公式
- **分数计算器**: 点击工具栏的"∑ 公式"按钮，使用交互式计算器
- **公式模板**: 选择预设的分数、带分数等模板
- **并排显示**: 多个算式自动并排排列，优化空间利用

### 3. 🤖 AI 助手使用
- **智能对话**: 点击右侧 AI 助手，获取排版建议
- **题目生成**: 选择题型和数量，一键生成考试题目
- **文档校对**: 使用 AI 检查错别字和语法错误
- **图像分析**: 上传图片让 AI 分析内容

### 4. ⚙️ 排版配置
- **页面设置**: 选择 A4/A3/Letter 等格式，设置边距和 DPI
- **字体设置**: 支持楷体、阿里巴巴普惠体等中文字体
- **段落设置**: 配置行高、段落间距和首行缩进
- **印刷设置**: CMYK 颜色模式和出血设置

### 5. 👁️ 预览和导出
- **实时预览**: HTML/PDF 双模式预览，支持缩放
- **版本切换**: 教师版/学生版切换（显示/隐藏答案）
- **高质量导出**: 600 DPI 高清 PDF 导出
- **批量处理**: 支持多文档批量转换

## 🧮 数学功能示例

### LaTeX 公式语法
```markdown
# 行内公式
这是一个分数：$\frac{1}{2}$，这是一个根号：$\sqrt{16} = 4$

# 块级公式
$$\frac{a^2 + b^2}{c^2 + d^2} = \frac{(a+bi)(a-bi)}{(c+di)(c-di)}$$

# 分数运算
$\frac{1}{2} + \frac{1}{4} = \frac{3}{4}$

# 带分数
$1\frac{1}{2} + 2\frac{1}{4} = 3\frac{3}{4}$
```

### 算式并排显示
```markdown
# 两个算式并排
$45 + 38 = 83$ $7 \times 12 = 84$

# 四个算式（两行）
$6 \times 7 = 42$ $\frac{1}{2} + \frac{1}{4} = \frac{3}{4}$ $\frac{3}{4} - \frac{1}{4} = \frac{1}{2}$ $1\frac{1}{2} + 2\frac{1}{4} = 3\frac{3}{4}$
```

### 分数计算器使用
1. 点击编辑器下方的"∑ 公式"按钮
2. 选择"分数运算"或"带分数"模板
3. 点击"显示计算器"
4. 输入分数进行计算
5. 点击"插入到编辑器"

## 📋 更新日志

### v2.0.0 (最新版本)
🎉 **重大更新 - AI 和数学功能全面升级**

#### 🧮 数学功能
- ✅ 完整的 LaTeX 数学公式支持
- ✅ 交互式分数计算器（支持带分数）
- ✅ 算式智能并排显示
- ✅ 600 DPI 高清数学公式渲染
- ✅ 数学公式模板库

#### 🤖 AI 智能助手
- ✅ 集成 Doubao AI 服务
- ✅ 智能题目生成（选择题、填空题、判断题、应用题、算数题）
- ✅ AI 文档校对和错误检查
- ✅ 图像上传和分析功能
- ✅ 智能排版建议

#### 🎨 界面优化
- ✅ 全新的数学工具栏
- ✅ 教师版/学生版切换
- ✅ 文件上传历史记录
- ✅ 响应式设计优化

#### 🔧 技术改进
- ✅ PDF 生成性能优化
- ✅ 数学公式渲染引擎升级
- ✅ 错误处理和用户体验改进

## 🧪 测试

### 后端测试
```bash
cd backend
pytest
```

### 前端测试
```bash
cd frontend
npm run test
```



## 🆘 故障排除

### 常见问题

1. **PDF 生成失败**
   - 检查 WeasyPrint 和 ReportLab 依赖是否正确安装
   - 确认字体文件存在且可访问
   - 检查数学公式语法是否正确

2. **数学公式不显示**
   - 确认 LaTeX 语法正确（使用 `$...$` 或 `$$...$$`）
   - 检查 Matplotlib 是否正确安装
   - 查看浏览器控制台是否有错误信息

3. **AI 功能不可用**
   - 检查是否配置了 `DOUBAO_API_KEY`
   - 确认 API 密钥有效且有足够额度
   - 检查网络连接是否正常

4. **分数计算器无法使用**
   - 确认输入的分数格式正确
   - 检查分子分母是否为正整数
   - 刷新页面重试

5. **文件上传失败**
   - 检查文件大小是否超过 50MB 限制
   - 确认文件格式为 `.md`, `.docx`, `.txt`
   - 检查文件是否损坏

### 日志查看

```bash
# 查看后端日志
docker-compose logs backend

# 查看前端日志
docker-compose logs frontend
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！我们特别欢迎以下类型的贡献：

### 🎯 贡献方向
- 🧮 **数学功能**: 新的数学公式模板、计算器功能
- 🤖 **AI 增强**: 更智能的排版建议、题目生成算法
- 🎨 **样式模板**: 新的文档样式、教育模板
- 🌐 **国际化**: 多语言支持
- 📱 **移动端**: 响应式设计优化
- 🔧 **性能优化**: 渲染速度、内存使用优化

### 开发指南
1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 🐛 报告问题
- 使用 GitHub Issues 报告 bug
- 提供详细的复现步骤
- 包含错误截图和日志信息

## 📞 支持

如有问题或建议，请：
- 提交 Issue 到 GitHub
- 查看文档和示例
- 参与社区讨论

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

- [Vue.js](https://vuejs.org/) - 前端框架
- [FastAPI](https://fastapi.tiangolo.com/) - 后端 API 框架
- [WeasyPrint](https://weasyprint.org/) - PDF 生成引擎
- [Doubao AI](https://www.volcengine.com/product/doubao) - AI 服务支持
- [MathJax](https://www.mathjax.org/) - 数学公式渲染

---

⭐ **如果这个项目对您有帮助，请给我们一个 Star！**

**PrintMind** - 让排版更智能，让印刷更专业 ✨
